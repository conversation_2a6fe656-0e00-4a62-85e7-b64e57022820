{"version": 3, "names": ["_gensync", "data", "require", "_functional", "_index", "_item", "_caching", "_resolveTargets", "isEqualDescriptor", "a", "b", "_a$file", "_b$file", "_a$file2", "_b$file2", "name", "value", "options", "dirname", "alias", "ownPass", "file", "request", "resolved", "handlerOf", "optionsWithResolvedBrowserslistConfigFile", "browserslistConfigFile", "resolveBrowserslistConfigFile", "createCachedDescriptors", "plugins", "presets", "passPerPreset", "createCachedPluginDescriptors", "createCachedPresetDescriptors", "createUncachedDescriptors", "once", "createPluginDescriptors", "createPresetDescriptors", "PRESET_DESCRIPTOR_CACHE", "WeakMap", "makeWeakCacheSync", "items", "cache", "using", "dir", "makeStrongCacheSync", "makeStrongCache", "descriptors", "map", "desc", "loadCachedDescriptor", "PLUGIN_DESCRIPTOR_CACHE", "DEFAULT_OPTIONS", "cacheByOptions", "get", "set", "possibilities", "includes", "matches", "filter", "possibility", "length", "push", "createDescriptors", "type", "gens<PERSON>", "all", "item", "index", "createDescriptor", "assertNoDuplicates", "pair", "getItemDescriptor", "Array", "isArray", "undefined", "filepath", "Error", "resolver", "loadPlugin", "loadPreset", "String", "__esModule", "default", "Map", "nameMap", "Set", "has", "conflicts", "i", "JSON", "stringify", "join", "add"], "sources": ["../../src/config/config-descriptors.ts"], "sourcesContent": ["import gensync, { type Hand<PERSON> } from \"gensync\";\nimport { once } from \"../gensync-utils/functional.ts\";\n\nimport { loadPlugin, loadPreset } from \"./files/index.ts\";\n\nimport { getItemDescriptor } from \"./item.ts\";\n\nimport {\n  makeWeakCacheSync,\n  makeStrongCacheSync,\n  makeStrongCache,\n} from \"./caching.ts\";\nimport type { CacheConfigurator } from \"./caching.ts\";\n\nimport type {\n  ValidatedOptions,\n  PluginList,\n  PluginItem,\n} from \"./validation/options.ts\";\n\nimport { resolveBrowserslistConfigFile } from \"./resolve-targets.ts\";\nimport type { PluginAPI, PresetAPI } from \"./helpers/config-api.ts\";\n\n// Represents a config object and functions to lazily load the descriptors\n// for the plugins and presets so we don't load the plugins/presets unless\n// the options object actually ends up being applicable.\nexport type OptionsAndDescriptors = {\n  options: ValidatedOptions;\n  plugins: () => <PERSON><PERSON><Array<UnloadedDescriptor<PluginAPI>>>;\n  presets: () => Handler<Array<UnloadedDescriptor<PresetAPI>>>;\n};\n\n// Represents a plugin or presets at a given location in a config object.\n// At this point these have been resolved to a specific object or function,\n// but have not yet been executed to call functions with options.\nexport interface UnloadedDescriptor<API, Options = object | undefined | false> {\n  name: string | undefined;\n  value: object | ((api: API, options: Options, dirname: string) => unknown);\n  options: Options;\n  dirname: string;\n  alias: string;\n  ownPass?: boolean;\n  file?: {\n    request: string;\n    resolved: string;\n  };\n}\n\nfunction isEqualDescriptor<API>(\n  a: UnloadedDescriptor<API>,\n  b: UnloadedDescriptor<API>,\n): boolean {\n  return (\n    a.name === b.name &&\n    a.value === b.value &&\n    a.options === b.options &&\n    a.dirname === b.dirname &&\n    a.alias === b.alias &&\n    a.ownPass === b.ownPass &&\n    a.file?.request === b.file?.request &&\n    a.file?.resolved === b.file?.resolved\n  );\n}\n\nexport type ValidatedFile = {\n  filepath: string;\n  dirname: string;\n  options: ValidatedOptions;\n};\n\n// eslint-disable-next-line require-yield\nfunction* handlerOf<T>(value: T): Handler<T> {\n  return value;\n}\n\nfunction optionsWithResolvedBrowserslistConfigFile(\n  options: ValidatedOptions,\n  dirname: string,\n): ValidatedOptions {\n  if (typeof options.browserslistConfigFile === \"string\") {\n    options.browserslistConfigFile = resolveBrowserslistConfigFile(\n      options.browserslistConfigFile,\n      dirname,\n    );\n  }\n  return options;\n}\n\n/**\n * Create a set of descriptors from a given options object, preserving\n * descriptor identity based on the identity of the plugin/preset arrays\n * themselves, and potentially on the identity of the plugins/presets + options.\n */\nexport function createCachedDescriptors(\n  dirname: string,\n  options: ValidatedOptions,\n  alias: string,\n): OptionsAndDescriptors {\n  const { plugins, presets, passPerPreset } = options;\n  return {\n    options: optionsWithResolvedBrowserslistConfigFile(options, dirname),\n    plugins: plugins\n      ? () =>\n          // @ts-expect-error todo(flow->ts) ts complains about incorrect arguments\n          // eslint-disable-next-line @typescript-eslint/no-use-before-define\n          createCachedPluginDescriptors(plugins, dirname)(alias)\n      : () => handlerOf([]),\n    presets: presets\n      ? () =>\n          // @ts-expect-error todo(flow->ts) ts complains about incorrect arguments\n          // eslint-disable-next-line @typescript-eslint/no-use-before-define\n          createCachedPresetDescriptors(presets, dirname)(alias)(\n            !!passPerPreset,\n          )\n      : () => handlerOf([]),\n  };\n}\n\n/**\n * Create a set of descriptors from a given options object, with consistent\n * identity for the descriptors, but not caching based on any specific identity.\n */\nexport function createUncachedDescriptors(\n  dirname: string,\n  options: ValidatedOptions,\n  alias: string,\n): OptionsAndDescriptors {\n  return {\n    options: optionsWithResolvedBrowserslistConfigFile(options, dirname),\n    // The returned result here is cached to represent a config object in\n    // memory, so we build and memoize the descriptors to ensure the same\n    // values are returned consistently.\n    plugins: once(() =>\n      createPluginDescriptors(options.plugins || [], dirname, alias),\n    ),\n    presets: once(() =>\n      createPresetDescriptors(\n        options.presets || [],\n        dirname,\n        alias,\n        !!options.passPerPreset,\n      ),\n    ),\n  };\n}\n\nconst PRESET_DESCRIPTOR_CACHE = new WeakMap();\nconst createCachedPresetDescriptors = makeWeakCacheSync(\n  (items: PluginList, cache: CacheConfigurator<string>) => {\n    const dirname = cache.using(dir => dir);\n    return makeStrongCacheSync((alias: string) =>\n      makeStrongCache(function* (\n        passPerPreset: boolean,\n      ): Handler<Array<UnloadedDescriptor<PresetAPI>>> {\n        const descriptors = yield* createPresetDescriptors(\n          items,\n          dirname,\n          alias,\n          passPerPreset,\n        );\n        return descriptors.map(\n          // Items are cached using the overall preset array identity when\n          // possibly, but individual descriptors are also cached if a match\n          // can be found in the previously-used descriptor lists.\n          desc => loadCachedDescriptor(PRESET_DESCRIPTOR_CACHE, desc),\n        );\n      }),\n    );\n  },\n);\n\nconst PLUGIN_DESCRIPTOR_CACHE = new WeakMap();\nconst createCachedPluginDescriptors = makeWeakCacheSync(\n  (items: PluginList, cache: CacheConfigurator<string>) => {\n    const dirname = cache.using(dir => dir);\n    return makeStrongCache(function* (\n      alias: string,\n    ): Handler<Array<UnloadedDescriptor<PluginAPI>>> {\n      const descriptors = yield* createPluginDescriptors(items, dirname, alias);\n      return descriptors.map(\n        // Items are cached using the overall plugin array identity when\n        // possibly, but individual descriptors are also cached if a match\n        // can be found in the previously-used descriptor lists.\n        desc => loadCachedDescriptor(PLUGIN_DESCRIPTOR_CACHE, desc),\n      );\n    });\n  },\n);\n\n/**\n * When no options object is given in a descriptor, this object is used\n * as a WeakMap key in order to have consistent identity.\n */\nconst DEFAULT_OPTIONS = {};\n\n/**\n * Given the cache and a descriptor, returns a matching descriptor from the\n * cache, or else returns the input descriptor and adds it to the cache for\n * next time.\n */\nfunction loadCachedDescriptor<API>(\n  cache: WeakMap<\n    object | Function,\n    WeakMap<object, Array<UnloadedDescriptor<API>>>\n  >,\n  desc: UnloadedDescriptor<API>,\n) {\n  const { value, options = DEFAULT_OPTIONS } = desc;\n  if (options === false) return desc;\n\n  let cacheByOptions = cache.get(value);\n  if (!cacheByOptions) {\n    cacheByOptions = new WeakMap();\n    cache.set(value, cacheByOptions);\n  }\n\n  let possibilities = cacheByOptions.get(options);\n  if (!possibilities) {\n    possibilities = [];\n    cacheByOptions.set(options, possibilities);\n  }\n\n  if (!possibilities.includes(desc)) {\n    const matches = possibilities.filter(possibility =>\n      isEqualDescriptor(possibility, desc),\n    );\n    if (matches.length > 0) {\n      return matches[0];\n    }\n\n    possibilities.push(desc);\n  }\n\n  return desc;\n}\n\nfunction* createPresetDescriptors(\n  items: PluginList,\n  dirname: string,\n  alias: string,\n  passPerPreset: boolean,\n): Handler<Array<UnloadedDescriptor<PresetAPI>>> {\n  return yield* createDescriptors(\n    \"preset\",\n    items,\n    dirname,\n    alias,\n    passPerPreset,\n  );\n}\n\nfunction* createPluginDescriptors(\n  items: PluginList,\n  dirname: string,\n  alias: string,\n): Handler<Array<UnloadedDescriptor<PluginAPI>>> {\n  return yield* createDescriptors(\"plugin\", items, dirname, alias);\n}\n\nfunction* createDescriptors<API>(\n  type: \"plugin\" | \"preset\",\n  items: PluginList,\n  dirname: string,\n  alias: string,\n  ownPass?: boolean,\n): Handler<Array<UnloadedDescriptor<API>>> {\n  const descriptors = yield* gensync.all(\n    items.map((item, index) =>\n      createDescriptor(item, dirname, {\n        type,\n        alias: `${alias}$${index}`,\n        ownPass: !!ownPass,\n      }),\n    ),\n  );\n\n  assertNoDuplicates(descriptors);\n\n  return descriptors;\n}\n\n/**\n * Given a plugin/preset item, resolve it into a standard format.\n */\nexport function* createDescriptor<API>(\n  pair: PluginItem,\n  dirname: string,\n  {\n    type,\n    alias,\n    ownPass,\n  }: {\n    type?: \"plugin\" | \"preset\";\n    alias: string;\n    ownPass?: boolean;\n  },\n): Handler<UnloadedDescriptor<API>> {\n  const desc = getItemDescriptor(pair);\n  if (desc) {\n    return desc;\n  }\n\n  let name;\n  let options;\n  // todo(flow->ts) better type annotation\n  let value: any = pair;\n  if (Array.isArray(value)) {\n    if (value.length === 3) {\n      [value, options, name] = value;\n    } else {\n      [value, options] = value;\n    }\n  }\n\n  let file = undefined;\n  let filepath = null;\n  if (typeof value === \"string\") {\n    if (typeof type !== \"string\") {\n      throw new Error(\n        \"To resolve a string-based item, the type of item must be given\",\n      );\n    }\n    const resolver = type === \"plugin\" ? loadPlugin : loadPreset;\n    const request = value;\n\n    ({ filepath, value } = yield* resolver(value, dirname));\n\n    file = {\n      request,\n      resolved: filepath,\n    };\n  }\n\n  if (!value) {\n    throw new Error(`Unexpected falsy value: ${String(value)}`);\n  }\n\n  if (typeof value === \"object\" && value.__esModule) {\n    if (value.default) {\n      value = value.default;\n    } else {\n      throw new Error(\"Must export a default export when using ES6 modules.\");\n    }\n  }\n\n  if (typeof value !== \"object\" && typeof value !== \"function\") {\n    throw new Error(\n      `Unsupported format: ${typeof value}. Expected an object or a function.`,\n    );\n  }\n\n  if (filepath !== null && typeof value === \"object\" && value) {\n    // We allow object values for plugins/presets nested directly within a\n    // config object, because it can be useful to define them in nested\n    // configuration contexts.\n    throw new Error(\n      `Plugin/Preset files are not allowed to export objects, only functions. In ${filepath}`,\n    );\n  }\n\n  return {\n    name,\n    alias: filepath || alias,\n    value,\n    options,\n    dirname,\n    ownPass,\n    file,\n  };\n}\n\nfunction assertNoDuplicates<API>(items: Array<UnloadedDescriptor<API>>): void {\n  const map = new Map();\n\n  for (const item of items) {\n    if (typeof item.value !== \"function\") continue;\n\n    let nameMap = map.get(item.value);\n    if (!nameMap) {\n      nameMap = new Set();\n      map.set(item.value, nameMap);\n    }\n\n    if (nameMap.has(item.name)) {\n      const conflicts = items.filter(i => i.value === item.value);\n      throw new Error(\n        [\n          `Duplicate plugin/preset detected.`,\n          `If you'd like to use two separate instances of a plugin,`,\n          `they need separate names, e.g.`,\n          ``,\n          `  plugins: [`,\n          `    ['some-plugin', {}],`,\n          `    ['some-plugin', {}, 'some unique name'],`,\n          `  ]`,\n          ``,\n          `Duplicates detected are:`,\n          `${JSON.stringify(conflicts, null, 2)}`,\n        ].join(\"\\n\"),\n      );\n    }\n\n    nameMap.add(item.name);\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAE,WAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AAaA,IAAAK,eAAA,GAAAL,OAAA;AA4BA,SAASM,iBAAiBA,CACxBC,CAA0B,EAC1BC,CAA0B,EACjB;EAAA,IAAAC,OAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,QAAA;EACT,OACEL,CAAC,CAACM,IAAI,KAAKL,CAAC,CAACK,IAAI,IACjBN,CAAC,CAACO,KAAK,KAAKN,CAAC,CAACM,KAAK,IACnBP,CAAC,CAACQ,OAAO,KAAKP,CAAC,CAACO,OAAO,IACvBR,CAAC,CAACS,OAAO,KAAKR,CAAC,CAACQ,OAAO,IACvBT,CAAC,CAACU,KAAK,KAAKT,CAAC,CAACS,KAAK,IACnBV,CAAC,CAACW,OAAO,KAAKV,CAAC,CAACU,OAAO,IACvB,EAAAT,OAAA,GAAAF,CAAC,CAACY,IAAI,qBAANV,OAAA,CAAQW,OAAO,QAAAV,OAAA,GAAKF,CAAC,CAACW,IAAI,qBAANT,OAAA,CAAQU,OAAO,KACnC,EAAAT,QAAA,GAAAJ,CAAC,CAACY,IAAI,qBAANR,QAAA,CAAQU,QAAQ,QAAAT,QAAA,GAAKJ,CAAC,CAACW,IAAI,qBAANP,QAAA,CAAQS,QAAQ;AAEzC;AASA,UAAUC,SAASA,CAAIR,KAAQ,EAAc;EAC3C,OAAOA,KAAK;AACd;AAEA,SAASS,yCAAyCA,CAChDR,OAAyB,EACzBC,OAAe,EACG;EAClB,IAAI,OAAOD,OAAO,CAACS,sBAAsB,KAAK,QAAQ,EAAE;IACtDT,OAAO,CAACS,sBAAsB,GAAG,IAAAC,6CAA6B,EAC5DV,OAAO,CAACS,sBAAsB,EAC9BR,OACF,CAAC;EACH;EACA,OAAOD,OAAO;AAChB;AAOO,SAASW,uBAAuBA,CACrCV,OAAe,EACfD,OAAyB,EACzBE,KAAa,EACU;EACvB,MAAM;IAAEU,OAAO;IAAEC,OAAO;IAAEC;EAAc,CAAC,GAAGd,OAAO;EACnD,OAAO;IACLA,OAAO,EAAEQ,yCAAyC,CAACR,OAAO,EAAEC,OAAO,CAAC;IACpEW,OAAO,EAAEA,OAAO,GACZ,MAGEG,6BAA6B,CAACH,OAAO,EAAEX,OAAO,CAAC,CAACC,KAAK,CAAC,GACxD,MAAMK,SAAS,CAAC,EAAE,CAAC;IACvBM,OAAO,EAAEA,OAAO,GACZ,MAGEG,6BAA6B,CAACH,OAAO,EAAEZ,OAAO,CAAC,CAACC,KAAK,CAAC,CACpD,CAAC,CAACY,aACJ,CAAC,GACH,MAAMP,SAAS,CAAC,EAAE;EACxB,CAAC;AACH;AAMO,SAASU,yBAAyBA,CACvChB,OAAe,EACfD,OAAyB,EACzBE,KAAa,EACU;EACvB,OAAO;IACLF,OAAO,EAAEQ,yCAAyC,CAACR,OAAO,EAAEC,OAAO,CAAC;IAIpEW,OAAO,EAAE,IAAAM,gBAAI,EAAC,MACZC,uBAAuB,CAACnB,OAAO,CAACY,OAAO,IAAI,EAAE,EAAEX,OAAO,EAAEC,KAAK,CAC/D,CAAC;IACDW,OAAO,EAAE,IAAAK,gBAAI,EAAC,MACZE,uBAAuB,CACrBpB,OAAO,CAACa,OAAO,IAAI,EAAE,EACrBZ,OAAO,EACPC,KAAK,EACL,CAAC,CAACF,OAAO,CAACc,aACZ,CACF;EACF,CAAC;AACH;AAEA,MAAMO,uBAAuB,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC7C,MAAMN,6BAA6B,GAAG,IAAAO,0BAAiB,EACrD,CAACC,KAAiB,EAAEC,KAAgC,KAAK;EACvD,MAAMxB,OAAO,GAAGwB,KAAK,CAACC,KAAK,CAACC,GAAG,IAAIA,GAAG,CAAC;EACvC,OAAO,IAAAC,4BAAmB,EAAE1B,KAAa,IACvC,IAAA2B,wBAAe,EAAC,WACdf,aAAsB,EACyB;IAC/C,MAAMgB,WAAW,GAAG,OAAOV,uBAAuB,CAChDI,KAAK,EACLvB,OAAO,EACPC,KAAK,EACLY,aACF,CAAC;IACD,OAAOgB,WAAW,CAACC,GAAG,CAIpBC,IAAI,IAAIC,oBAAoB,CAACZ,uBAAuB,EAAEW,IAAI,CAC5D,CAAC;EACH,CAAC,CACH,CAAC;AACH,CACF,CAAC;AAED,MAAME,uBAAuB,GAAG,IAAIZ,OAAO,CAAC,CAAC;AAC7C,MAAMP,6BAA6B,GAAG,IAAAQ,0BAAiB,EACrD,CAACC,KAAiB,EAAEC,KAAgC,KAAK;EACvD,MAAMxB,OAAO,GAAGwB,KAAK,CAACC,KAAK,CAACC,GAAG,IAAIA,GAAG,CAAC;EACvC,OAAO,IAAAE,wBAAe,EAAC,WACrB3B,KAAa,EACkC;IAC/C,MAAM4B,WAAW,GAAG,OAAOX,uBAAuB,CAACK,KAAK,EAAEvB,OAAO,EAAEC,KAAK,CAAC;IACzE,OAAO4B,WAAW,CAACC,GAAG,CAIpBC,IAAI,IAAIC,oBAAoB,CAACC,uBAAuB,EAAEF,IAAI,CAC5D,CAAC;EACH,CAAC,CAAC;AACJ,CACF,CAAC;AAMD,MAAMG,eAAe,GAAG,CAAC,CAAC;AAO1B,SAASF,oBAAoBA,CAC3BR,KAGC,EACDO,IAA6B,EAC7B;EACA,MAAM;IAAEjC,KAAK;IAAEC,OAAO,GAAGmC;EAAgB,CAAC,GAAGH,IAAI;EACjD,IAAIhC,OAAO,KAAK,KAAK,EAAE,OAAOgC,IAAI;EAElC,IAAII,cAAc,GAAGX,KAAK,CAACY,GAAG,CAACtC,KAAK,CAAC;EACrC,IAAI,CAACqC,cAAc,EAAE;IACnBA,cAAc,GAAG,IAAId,OAAO,CAAC,CAAC;IAC9BG,KAAK,CAACa,GAAG,CAACvC,KAAK,EAAEqC,cAAc,CAAC;EAClC;EAEA,IAAIG,aAAa,GAAGH,cAAc,CAACC,GAAG,CAACrC,OAAO,CAAC;EAC/C,IAAI,CAACuC,aAAa,EAAE;IAClBA,aAAa,GAAG,EAAE;IAClBH,cAAc,CAACE,GAAG,CAACtC,OAAO,EAAEuC,aAAa,CAAC;EAC5C;EAEA,IAAI,CAACA,aAAa,CAACC,QAAQ,CAACR,IAAI,CAAC,EAAE;IACjC,MAAMS,OAAO,GAAGF,aAAa,CAACG,MAAM,CAACC,WAAW,IAC9CpD,iBAAiB,CAACoD,WAAW,EAAEX,IAAI,CACrC,CAAC;IACD,IAAIS,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MACtB,OAAOH,OAAO,CAAC,CAAC,CAAC;IACnB;IAEAF,aAAa,CAACM,IAAI,CAACb,IAAI,CAAC;EAC1B;EAEA,OAAOA,IAAI;AACb;AAEA,UAAUZ,uBAAuBA,CAC/BI,KAAiB,EACjBvB,OAAe,EACfC,KAAa,EACbY,aAAsB,EACyB;EAC/C,OAAO,OAAOgC,iBAAiB,CAC7B,QAAQ,EACRtB,KAAK,EACLvB,OAAO,EACPC,KAAK,EACLY,aACF,CAAC;AACH;AAEA,UAAUK,uBAAuBA,CAC/BK,KAAiB,EACjBvB,OAAe,EACfC,KAAa,EACkC;EAC/C,OAAO,OAAO4C,iBAAiB,CAAC,QAAQ,EAAEtB,KAAK,EAAEvB,OAAO,EAAEC,KAAK,CAAC;AAClE;AAEA,UAAU4C,iBAAiBA,CACzBC,IAAyB,EACzBvB,KAAiB,EACjBvB,OAAe,EACfC,KAAa,EACbC,OAAiB,EACwB;EACzC,MAAM2B,WAAW,GAAG,OAAOkB,SAAMA,CAAC,CAACC,GAAG,CACpCzB,KAAK,CAACO,GAAG,CAAC,CAACmB,IAAI,EAAEC,KAAK,KACpBC,gBAAgB,CAACF,IAAI,EAAEjD,OAAO,EAAE;IAC9B8C,IAAI;IACJ7C,KAAK,EAAE,GAAGA,KAAK,IAAIiD,KAAK,EAAE;IAC1BhD,OAAO,EAAE,CAAC,CAACA;EACb,CAAC,CACH,CACF,CAAC;EAEDkD,kBAAkB,CAACvB,WAAW,CAAC;EAE/B,OAAOA,WAAW;AACpB;AAKO,UAAUsB,gBAAgBA,CAC/BE,IAAgB,EAChBrD,OAAe,EACf;EACE8C,IAAI;EACJ7C,KAAK;EACLC;AAKF,CAAC,EACiC;EAClC,MAAM6B,IAAI,GAAG,IAAAuB,uBAAiB,EAACD,IAAI,CAAC;EACpC,IAAItB,IAAI,EAAE;IACR,OAAOA,IAAI;EACb;EAEA,IAAIlC,IAAI;EACR,IAAIE,OAAO;EAEX,IAAID,KAAU,GAAGuD,IAAI;EACrB,IAAIE,KAAK,CAACC,OAAO,CAAC1D,KAAK,CAAC,EAAE;IACxB,IAAIA,KAAK,CAAC6C,MAAM,KAAK,CAAC,EAAE;MACtB,CAAC7C,KAAK,EAAEC,OAAO,EAAEF,IAAI,CAAC,GAAGC,KAAK;IAChC,CAAC,MAAM;MACL,CAACA,KAAK,EAAEC,OAAO,CAAC,GAAGD,KAAK;IAC1B;EACF;EAEA,IAAIK,IAAI,GAAGsD,SAAS;EACpB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAI,OAAO5D,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,OAAOgD,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIa,KAAK,CACb,gEACF,CAAC;IACH;IACA,MAAMC,QAAQ,GAAGd,IAAI,KAAK,QAAQ,GAAGe,iBAAU,GAAGC,iBAAU;IAC5D,MAAM1D,OAAO,GAAGN,KAAK;IAErB,CAAC;MAAE4D,QAAQ;MAAE5D;IAAM,CAAC,GAAG,OAAO8D,QAAQ,CAAC9D,KAAK,EAAEE,OAAO,CAAC;IAEtDG,IAAI,GAAG;MACLC,OAAO;MACPC,QAAQ,EAAEqD;IACZ,CAAC;EACH;EAEA,IAAI,CAAC5D,KAAK,EAAE;IACV,MAAM,IAAI6D,KAAK,CAAC,2BAA2BI,MAAM,CAACjE,KAAK,CAAC,EAAE,CAAC;EAC7D;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACkE,UAAU,EAAE;IACjD,IAAIlE,KAAK,CAACmE,OAAO,EAAE;MACjBnE,KAAK,GAAGA,KAAK,CAACmE,OAAO;IACvB,CAAC,MAAM;MACL,MAAM,IAAIN,KAAK,CAAC,sDAAsD,CAAC;IACzE;EACF;EAEA,IAAI,OAAO7D,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC5D,MAAM,IAAI6D,KAAK,CACb,uBAAuB,OAAO7D,KAAK,qCACrC,CAAC;EACH;EAEA,IAAI4D,QAAQ,KAAK,IAAI,IAAI,OAAO5D,KAAK,KAAK,QAAQ,IAAIA,KAAK,EAAE;IAI3D,MAAM,IAAI6D,KAAK,CACb,6EAA6ED,QAAQ,EACvF,CAAC;EACH;EAEA,OAAO;IACL7D,IAAI;IACJI,KAAK,EAAEyD,QAAQ,IAAIzD,KAAK;IACxBH,KAAK;IACLC,OAAO;IACPC,OAAO;IACPE,OAAO;IACPC;EACF,CAAC;AACH;AAEA,SAASiD,kBAAkBA,CAAM7B,KAAqC,EAAQ;EAC5E,MAAMO,GAAG,GAAG,IAAIoC,GAAG,CAAC,CAAC;EAErB,KAAK,MAAMjB,IAAI,IAAI1B,KAAK,EAAE;IACxB,IAAI,OAAO0B,IAAI,CAACnD,KAAK,KAAK,UAAU,EAAE;IAEtC,IAAIqE,OAAO,GAAGrC,GAAG,CAACM,GAAG,CAACa,IAAI,CAACnD,KAAK,CAAC;IACjC,IAAI,CAACqE,OAAO,EAAE;MACZA,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;MACnBtC,GAAG,CAACO,GAAG,CAACY,IAAI,CAACnD,KAAK,EAAEqE,OAAO,CAAC;IAC9B;IAEA,IAAIA,OAAO,CAACE,GAAG,CAACpB,IAAI,CAACpD,IAAI,CAAC,EAAE;MAC1B,MAAMyE,SAAS,GAAG/C,KAAK,CAACkB,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACzE,KAAK,KAAKmD,IAAI,CAACnD,KAAK,CAAC;MAC3D,MAAM,IAAI6D,KAAK,CACb,CACE,mCAAmC,EACnC,0DAA0D,EAC1D,gCAAgC,EAChC,EAAE,EACF,cAAc,EACd,0BAA0B,EAC1B,8CAA8C,EAC9C,KAAK,EACL,EAAE,EACF,0BAA0B,EAC1B,GAAGa,IAAI,CAACC,SAAS,CAACH,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CACxC,CAACI,IAAI,CAAC,IAAI,CACb,CAAC;IACH;IAEAP,OAAO,CAACQ,GAAG,CAAC1B,IAAI,CAACpD,IAAI,CAAC;EACxB;AACF;AAAC", "ignoreList": []}